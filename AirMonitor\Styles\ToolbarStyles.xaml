<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:AirMonitor.Controls"
                    xmlns:converters="clr-namespace:AirMonitor.Converters">

    <!-- 合并转换器资源 -->
    <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

    <!-- ========================================
         Fluent Design Toolbar Styles
         符合Windows 11 Fluent Design规范的工具栏样式
         ======================================== -->

    <!-- 工具栏按钮基础样式 -->
    <Style x:Key="ToolbarButtonBaseStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Background" Value="{DynamicResource ToolbarButtonBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinWidth" Value="32"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            UseLayoutRounding="{TemplateBinding UseLayoutRounding}">
                        <Border x:Name="FocusBorder"
                                BorderThickness="2"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                BorderBrush="Transparent">
                            <ContentPresenter x:Name="ContentPresenter"
                                            Content="{TemplateBinding Content}"
                                            ContentTemplate="{TemplateBinding ContentTemplate}"
                                            ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"
                                            RecognizesAccessKey="True"
                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- Normal State -->
                        <Trigger Property="IsMouseOver" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarButtonBackgroundBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundBrush}"/>
                        </Trigger>
                        
                        <!-- MouseOver State -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarButtonBackgroundPointerOverBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderPointerOverBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundPointerOverBrush}"/>
                        </Trigger>
                        
                        <!-- Pressed State -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarButtonBackgroundPressedBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderPressedBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundPressedBrush}"/>
                        </Trigger>
                        
                        <!-- Disabled State -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarButtonBackgroundDisabledBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderDisabledBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundDisabledBrush}"/>
                        </Trigger>
                        
                        <!-- Focus State -->
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="FocusBorder" Property="BorderBrush" Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                        </Trigger>
                        
                        <!-- Keyboard Focus State -->
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter TargetName="FocusBorder" Property="BorderBrush" Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏图标按钮样式 -->
    <Style x:Key="ToolbarIconButtonStyle" TargetType="Button" BasedOn="{StaticResource ToolbarButtonBaseStyle}">
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding" Value="6"/>
        <Setter Property="ToolTipService.ShowOnDisabled" Value="True"/>
        <Setter Property="ToolTipService.Placement" Value="Bottom"/>
        <Setter Property="ToolTipService.InitialShowDelay" Value="800"/>
        <Setter Property="ToolTipService.ShowDuration" Value="5000"/>
    </Style>

    <!-- 工具栏文本按钮样式 -->
    <Style x:Key="ToolbarTextButtonStyle" TargetType="Button" BasedOn="{StaticResource ToolbarButtonBaseStyle}">
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="MinWidth" Value="60"/>
    </Style>

    <!-- 工具栏分隔符样式 -->
    <Style x:Key="ToolbarSeparatorStyle" TargetType="Separator">
        <Setter Property="Width" Value="1"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="4,0"/>
        <Setter Property="Background" Value="{DynamicResource ToolbarSeparatorBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Separator">
                    <Rectangle Fill="{TemplateBinding Background}"
                             Width="{TemplateBinding Width}"
                             Height="{TemplateBinding Height}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏下拉按钮样式 -->
    <Style x:Key="ToolbarDropDownButtonStyle" TargetType="Button" BasedOn="{StaticResource ToolbarButtonBaseStyle}">
        <Setter Property="Padding" Value="8,6,20,6"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            UseLayoutRounding="{TemplateBinding UseLayoutRounding}">
                        <Border x:Name="FocusBorder"
                                BorderThickness="2"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                BorderBrush="Transparent">
                            <Grid>
                                <ContentPresenter x:Name="ContentPresenter"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                                ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                Margin="{TemplateBinding Padding}"
                                                RecognizesAccessKey="True"
                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                
                                <!-- 下拉箭头 -->
                                <Path x:Name="DropDownArrow"
                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                      Fill="{TemplateBinding Foreground}"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Center"
                                      Margin="0,0,6,0"
                                      Width="8"
                                      Height="4"/>
                            </Grid>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 继承基础按钮的所有触发器 -->
                        <Trigger Property="IsMouseOver" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarDropDownButtonBackgroundBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundBrush}"/>
                        </Trigger>
                        
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarDropDownButtonBackgroundPointerOverBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderPointerOverBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundPointerOverBrush}"/>
                        </Trigger>
                        
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarDropDownButtonBackgroundPressedBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderPressedBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundPressedBrush}"/>
                        </Trigger>
                        
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ToolbarDropDownButtonBackgroundDisabledBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ToolbarButtonBorderDisabledBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource ToolbarButtonForegroundDisabledBrush}"/>
                        </Trigger>
                        
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="FocusBorder" Property="BorderBrush" Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                        </Trigger>
                        
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter TargetName="FocusBorder" Property="BorderBrush" Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏主容器样式 -->
    <Style x:Key="FluentToolbarStyle" TargetType="controls:FluentToolbar">
        <Setter Property="Background" Value="{DynamicResource ToolbarBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ToolbarBorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Padding" Value="8,0"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Continue"/>
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="Continue"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:FluentToolbar">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            UseLayoutRounding="{TemplateBinding UseLayoutRounding}"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <ScrollViewer x:Name="PART_ScrollViewer"
                                    HorizontalScrollBarVisibility="Auto"
                                    VerticalScrollBarVisibility="Disabled"
                                    CanContentScroll="True"
                                    Focusable="False">
                            <ItemsPresenter Margin="{TemplateBinding Padding}"/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"
                              VerticalAlignment="Center"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏项容器样式 -->
    <Style x:Key="ToolbarItemContainerStyle" TargetType="ContentPresenter">
        <Setter Property="Margin" Value="2,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
    </Style>

    <!-- 工具栏组样式 -->
    <Style x:Key="ToolbarGroupStyle" TargetType="controls:ToolbarGroup">
        <Setter Property="Margin" Value="4,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:ToolbarGroup">
                    <StackPanel Orientation="Horizontal"
                              VerticalAlignment="Center">
                        <ItemsPresenter/>
                        <!-- 组分隔符 -->
                        <Separator Style="{StaticResource ToolbarSeparatorStyle}"
                                 Visibility="{TemplateBinding ShowSeparator, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 Margin="4,0,0,0"/>
                    </StackPanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏按钮项样式 -->
    <Style x:Key="ToolbarButtonItemStyle" TargetType="controls:ToolbarButtonItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:ToolbarButtonItem">
                    <Button Style="{StaticResource ToolbarIconButtonStyle}"
                            Command="{TemplateBinding Command}"
                            CommandParameter="{TemplateBinding CommandParameter}"
                            ToolTip="{TemplateBinding ToolTip}"
                            IsEnabled="{TemplateBinding IsEnabled}">
                        <StackPanel Orientation="Vertical"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center">
                            <!-- 图标 -->
                            <ContentPresenter Content="{TemplateBinding Icon}"
                                            Width="16" Height="16"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                            <!-- 文本 (可选) -->
                            <TextBlock Text="{TemplateBinding Text}"
                                     Style="{StaticResource IconButtonTextStyle}"
                                     Visibility="{TemplateBinding ShowText, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     HorizontalAlignment="Center"
                                     TextTrimming="CharacterEllipsis"
                                     MaxWidth="60"/>
                        </StackPanel>
                    </Button>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 工具栏下拉按钮项样式 -->
    <Style x:Key="ToolbarDropDownItemStyle" TargetType="controls:ToolbarDropDownItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:ToolbarDropDownItem">
                    <Button Style="{StaticResource ToolbarDropDownButtonStyle}"
                            Command="{TemplateBinding Command}"
                            CommandParameter="{TemplateBinding CommandParameter}"
                            ToolTip="{TemplateBinding ToolTip}"
                            IsEnabled="{TemplateBinding IsEnabled}">
                        <StackPanel Orientation="Horizontal">
                            <!-- 图标 -->
                            <ContentPresenter Content="{TemplateBinding Icon}"
                                            Width="16" Height="16"
                                            Margin="0,0,4,0"
                                            VerticalAlignment="Center"
                                            Visibility="{TemplateBinding Icon, Converter={StaticResource NullToVisibilityConverter}}"/>
                            <!-- 文本 -->
                            <TextBlock Text="{TemplateBinding Text}"
                                     VerticalAlignment="Center"
                                     Style="{StaticResource SecondaryButtonTextStyle}"/>
                        </StackPanel>
                    </Button>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
