using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace AirMonitor.Converters;

/// <summary>
/// 数值比较到布尔值转换器
/// </summary>
[ValueConversion(typeof(double), typeof(bool))]
public class NumberComparisonToBooleanConverter : IValueConverter
{
    public ComparisonType Comparison { get; set; } = ComparisonType.GreaterThan;
    public double ComparisonValue { get; set; } = 0;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double doubleValue)
        {
            return Comparison switch
            {
                ComparisonType.GreaterThan => doubleValue > ComparisonValue,
                ComparisonType.GreaterThanOrEqual => doubleValue >= ComparisonValue,
                ComparisonType.LessThan => doubleValue < ComparisonValue,
                ComparisonType.LessThanOrEqual => doubleValue <= ComparisonValue,
                ComparisonType.Equal => Math.Abs(doubleValue - ComparisonValue) < 0.001,
                ComparisonType.NotEqual => Math.Abs(doubleValue - ComparisonValue) >= 0.001,
                _ => false
            };
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 比较类型枚举
/// </summary>
public enum ComparisonType
{
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Equal,
    NotEqual
}

/// <summary>
/// 多值布尔与转换器
/// </summary>
public class MultiBooleanAndConverter : IMultiValueConverter
{
    public static readonly MultiBooleanAndConverter Instance = new();

    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Length == 0)
            return false;

        foreach (var value in values)
        {
            if (value is bool boolValue && !boolValue)
                return false;
        }
        return true;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 多值布尔或转换器
/// </summary>
public class MultiBooleanOrConverter : IMultiValueConverter
{
    public static readonly MultiBooleanOrConverter Instance = new();

    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Length == 0)
            return false;

        foreach (var value in values)
        {
            if (value is bool boolValue && boolValue)
                return true;
        }
        return false;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 工具栏项类型到样式转换器
/// </summary>
[ValueConversion(typeof(object), typeof(Style))]
public class ToolbarItemTypeToStyleConverter : IValueConverter
{
    public Style? ButtonStyle { get; set; }
    public Style? DropDownStyle { get; set; }
    public Style? SeparatorStyle { get; set; }

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value switch
        {
            Controls.ToolbarButtonItem => ButtonStyle,
            Controls.ToolbarDropDownItem => DropDownStyle,
            Separator => SeparatorStyle,
            _ => null
        } ?? DependencyProperty.UnsetValue;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 图标大小转换器
/// </summary>
[ValueConversion(typeof(double), typeof(double))]
public class IconSizeConverter : IValueConverter
{
    public double SmallSize { get; set; } = 16;
    public double MediumSize { get; set; } = 20;
    public double LargeSize { get; set; } = 24;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string sizeString)
        {
            return sizeString.ToLower() switch
            {
                "small" => SmallSize,
                "medium" => MediumSize,
                "large" => LargeSize,
                _ => MediumSize
            };
        }
        
        if (value is double size)
        {
            return size;
        }
        
        return MediumSize;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
