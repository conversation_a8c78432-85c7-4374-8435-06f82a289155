using System.Windows;
using System.Windows.Controls;
using System.Windows.Automation.Peers;

namespace AirMonitor.Controls;

/// <summary>
/// 工具栏组控件，用于将相关的工具栏项分组显示
/// </summary>
public class ToolbarGroup : ItemsControl
{
    static ToolbarGroup()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(ToolbarGroup),
            new FrameworkPropertyMetadata(typeof(ToolbarGroup)));
    }

    public ToolbarGroup()
    {
        // 设置默认项容器
        ItemContainerGenerator.StatusChanged += OnItemContainerGeneratorStatusChanged;
    }

    #region 依赖属性

    /// <summary>
    /// 组标题
    /// </summary>
    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(
            nameof(Title),
            typeof(string),
            typeof(ToolbarGroup),
            new PropertyMetadata(string.Empty));

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    /// <summary>
    /// 是否显示分隔符
    /// </summary>
    public static readonly DependencyProperty ShowSeparatorProperty =
        DependencyProperty.Register(
            nameof(ShowSeparator),
            typeof(bool),
            typeof(ToolbarGroup),
            new PropertyMetadata(true));

    public bool ShowSeparator
    {
        get => (bool)GetValue(ShowSeparatorProperty);
        set => SetValue(ShowSeparatorProperty, value);
    }

    /// <summary>
    /// 组方向
    /// </summary>
    public static readonly DependencyProperty OrientationProperty =
        DependencyProperty.Register(
            nameof(Orientation),
            typeof(Orientation),
            typeof(ToolbarGroup),
            new PropertyMetadata(Orientation.Horizontal));

    public Orientation Orientation
    {
        get => (Orientation)GetValue(OrientationProperty);
        set => SetValue(OrientationProperty, value);
    }

    /// <summary>
    /// 是否可折叠
    /// </summary>
    public static readonly DependencyProperty IsCollapsibleProperty =
        DependencyProperty.Register(
            nameof(IsCollapsible),
            typeof(bool),
            typeof(ToolbarGroup),
            new PropertyMetadata(false));

    public bool IsCollapsible
    {
        get => (bool)GetValue(IsCollapsibleProperty);
        set => SetValue(IsCollapsibleProperty, value);
    }

    /// <summary>
    /// 是否已折叠
    /// </summary>
    public static readonly DependencyProperty IsCollapsedProperty =
        DependencyProperty.Register(
            nameof(IsCollapsed),
            typeof(bool),
            typeof(ToolbarGroup),
            new PropertyMetadata(false, OnIsCollapsedChanged));

    public bool IsCollapsed
    {
        get => (bool)GetValue(IsCollapsedProperty);
        set => SetValue(IsCollapsedProperty, value);
    }

    /// <summary>
    /// 折叠阈值宽度
    /// </summary>
    public static readonly DependencyProperty CollapseThresholdProperty =
        DependencyProperty.Register(
            nameof(CollapseThreshold),
            typeof(double),
            typeof(ToolbarGroup),
            new PropertyMetadata(200.0));

    public double CollapseThreshold
    {
        get => (double)GetValue(CollapseThresholdProperty);
        set => SetValue(CollapseThresholdProperty, value);
    }

    #endregion

    #region 事件

    /// <summary>
    /// 折叠状态改变事件
    /// </summary>
    public static readonly RoutedEvent CollapsedChangedEvent =
        EventManager.RegisterRoutedEvent(
            nameof(CollapsedChanged),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(ToolbarGroup));

    public event RoutedEventHandler CollapsedChanged
    {
        add => AddHandler(CollapsedChangedEvent, value);
        remove => RemoveHandler(CollapsedChangedEvent, value);
    }

    #endregion

    #region 折叠逻辑

    private static void OnIsCollapsedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ToolbarGroup group)
        {
            group.OnCollapsedChanged((bool)e.NewValue);
        }
    }

    protected virtual void OnCollapsedChanged(bool isCollapsed)
    {
        UpdateVisualState();
        RaiseEvent(new RoutedEventArgs(CollapsedChangedEvent, this));
    }

    protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
    {
        base.OnRenderSizeChanged(sizeInfo);
        
        if (IsCollapsible)
        {
            CheckCollapseCondition();
        }
    }

    private void CheckCollapseCondition()
    {
        if (Parent is FrameworkElement parent)
        {
            var availableWidth = parent.ActualWidth;
            var shouldCollapse = availableWidth < CollapseThreshold;
            
            if (shouldCollapse != IsCollapsed)
            {
                IsCollapsed = shouldCollapse;
            }
        }
    }

    private void UpdateVisualState()
    {
        if (IsCollapsed)
        {
            VisualStateManager.GoToState(this, "Collapsed", true);
        }
        else
        {
            VisualStateManager.GoToState(this, "Expanded", true);
        }
    }

    #endregion

    #region 项容器管理

    protected override DependencyObject GetContainerForItemOverride()
    {
        return new ContentPresenter();
    }

    protected override bool IsItemItsOwnContainerOverride(object item)
    {
        return item is ContentPresenter || item is ToolbarItemBase;
    }

    protected override void PrepareContainerForItemOverride(DependencyObject element, object item)
    {
        base.PrepareContainerForItemOverride(element, item);
        
        if (element is FrameworkElement container)
        {
            // 设置容器的基本属性
            container.Margin = new Thickness(2, 0, 2, 0);
            container.VerticalAlignment = VerticalAlignment.Center;
            
            // 如果项是工具栏项基类，设置其父组引用
            if (item is ToolbarItemBase toolbarItem)
            {
                toolbarItem.SetValue(FrameworkElement.DataContextProperty, this);
            }
        }
    }

    private void OnItemContainerGeneratorStatusChanged(object? sender, EventArgs e)
    {
        if (ItemContainerGenerator.Status == System.Windows.Controls.Primitives.GeneratorStatus.ContainersGenerated)
        {
            UpdateItemContainers();
        }
    }

    private void UpdateItemContainers()
    {
        for (int i = 0; i < Items.Count; i++)
        {
            var container = ItemContainerGenerator.ContainerFromIndex(i) as FrameworkElement;
            if (container != null)
            {
                // 为每个容器设置适当的样式和行为
                SetupItemContainer(container, Items[i]);
            }
        }
    }

    private void SetupItemContainer(FrameworkElement container, object item)
    {
        // 设置键盘导航
        if (container is Control control)
        {
            control.IsTabStop = true;
            control.Focusable = true;
        }
        
        // 设置无障碍访问属性
        if (!string.IsNullOrEmpty(Title))
        {
            AutomationProperties.SetName(container, $"{Title} 组中的项目");
        }
    }

    #endregion

    #region 无障碍访问支持

    protected override AutomationPeer OnCreateAutomationPeer()
    {
        return new ToolbarGroupAutomationPeer(this);
    }

    #endregion
}

/// <summary>
/// ToolbarGroup的自动化对等类
/// </summary>
public class ToolbarGroupAutomationPeer : ItemsControlAutomationPeer
{
    public ToolbarGroupAutomationPeer(ToolbarGroup owner) : base(owner)
    {
    }

    protected override string GetClassNameCore()
    {
        return "ToolbarGroup";
    }

    protected override AutomationControlType GetAutomationControlTypeCore()
    {
        return AutomationControlType.Group;
    }

    protected override string GetLocalizedControlTypeCore()
    {
        return "工具栏组";
    }

    protected override string GetNameCore()
    {
        var group = Owner as ToolbarGroup;
        return !string.IsNullOrEmpty(group?.Title) ? group.Title : base.GetNameCore();
    }
}
