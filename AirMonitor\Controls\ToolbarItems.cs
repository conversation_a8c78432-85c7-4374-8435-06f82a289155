using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Automation.Peers;

namespace AirMonitor.Controls;

/// <summary>
/// 工具栏项基类
/// </summary>
public abstract class ToolbarItemBase : Control
{
    static ToolbarItemBase()
    {
        IsTabStopProperty.OverrideMetadata(typeof(ToolbarItemBase), new FrameworkPropertyMetadata(true));
        FocusableProperty.OverrideMetadata(typeof(ToolbarItemBase), new FrameworkPropertyMetadata(true));
    }

    #region 依赖属性

    /// <summary>
    /// 图标内容
    /// </summary>
    public static readonly DependencyProperty IconProperty =
        DependencyProperty.Register(
            nameof(Icon),
            typeof(object),
            typeof(ToolbarItemBase),
            new PropertyMetadata(null));

    public object Icon
    {
        get => GetValue(IconProperty);
        set => SetValue(IconProperty, value);
    }

    /// <summary>
    /// 显示文本
    /// </summary>
    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(
            nameof(Text),
            typeof(string),
            typeof(ToolbarItemBase),
            new PropertyMetadata(string.Empty));

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    /// <summary>
    /// 工具提示
    /// </summary>
    public static readonly DependencyProperty ToolTipProperty =
        DependencyProperty.Register(
            nameof(ToolTip),
            typeof(object),
            typeof(ToolbarItemBase),
            new PropertyMetadata(null));

    public new object ToolTip
    {
        get => GetValue(ToolTipProperty);
        set => SetValue(ToolTipProperty, value);
    }

    /// <summary>
    /// 命令
    /// </summary>
    public static readonly DependencyProperty CommandProperty =
        DependencyProperty.Register(
            nameof(Command),
            typeof(ICommand),
            typeof(ToolbarItemBase),
            new PropertyMetadata(null, OnCommandChanged));

    public ICommand Command
    {
        get => (ICommand)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    /// <summary>
    /// 命令参数
    /// </summary>
    public static readonly DependencyProperty CommandParameterProperty =
        DependencyProperty.Register(
            nameof(CommandParameter),
            typeof(object),
            typeof(ToolbarItemBase),
            new PropertyMetadata(null));

    public object CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    /// <summary>
    /// 命令目标
    /// </summary>
    public static readonly DependencyProperty CommandTargetProperty =
        DependencyProperty.Register(
            nameof(CommandTarget),
            typeof(IInputElement),
            typeof(ToolbarItemBase),
            new PropertyMetadata(null));

    public IInputElement CommandTarget
    {
        get => (IInputElement)GetValue(CommandTargetProperty);
        set => SetValue(CommandTargetProperty, value);
    }

    #endregion

    #region 命令处理

    private static void OnCommandChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ToolbarItemBase item)
        {
            if (e.OldValue is ICommand oldCommand)
            {
                oldCommand.CanExecuteChanged -= item.OnCanExecuteChanged;
            }

            if (e.NewValue is ICommand newCommand)
            {
                newCommand.CanExecuteChanged += item.OnCanExecuteChanged;
                item.UpdateCanExecute();
            }
        }
    }

    private void OnCanExecuteChanged(object? sender, EventArgs e)
    {
        UpdateCanExecute();
    }

    private void UpdateCanExecute()
    {
        IsEnabled = Command?.CanExecute(CommandParameter) ?? true;
    }

    protected virtual void ExecuteCommand()
    {
        if (Command?.CanExecute(CommandParameter) == true)
        {
            Command.Execute(CommandParameter);
        }
    }

    #endregion

    #region 键盘支持

    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.Enter || e.Key == Key.Space)
        {
            ExecuteCommand();
            e.Handled = true;
        }
        else
        {
            base.OnKeyDown(e);
        }
    }

    #endregion
}

/// <summary>
/// 工具栏按钮项
/// </summary>
public class ToolbarButtonItem : ToolbarItemBase, ICommandSource
{
    static ToolbarButtonItem()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(ToolbarButtonItem),
            new FrameworkPropertyMetadata(typeof(ToolbarButtonItem)));
    }

    #region 依赖属性

    /// <summary>
    /// 是否显示文本
    /// </summary>
    public static readonly DependencyProperty ShowTextProperty =
        DependencyProperty.Register(
            nameof(ShowText),
            typeof(bool),
            typeof(ToolbarButtonItem),
            new PropertyMetadata(false));

    public bool ShowText
    {
        get => (bool)GetValue(ShowTextProperty);
        set => SetValue(ShowTextProperty, value);
    }

    #endregion

    #region 事件

    /// <summary>
    /// 点击事件
    /// </summary>
    public static readonly RoutedEvent ClickEvent =
        EventManager.RegisterRoutedEvent(
            nameof(Click),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(ToolbarButtonItem));

    public event RoutedEventHandler Click
    {
        add => AddHandler(ClickEvent, value);
        remove => RemoveHandler(ClickEvent, value);
    }

    #endregion

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        base.OnMouseLeftButtonUp(e);
        
        if (IsMouseOver)
        {
            OnClick();
        }
    }

    protected virtual void OnClick()
    {
        ExecuteCommand();
        RaiseEvent(new RoutedEventArgs(ClickEvent, this));
    }

    protected override AutomationPeer OnCreateAutomationPeer()
    {
        return new ToolbarButtonItemAutomationPeer(this);
    }
}

/// <summary>
/// 工具栏下拉按钮项
/// </summary>
public class ToolbarDropDownItem : ToolbarItemBase, ICommandSource
{
    static ToolbarDropDownItem()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(ToolbarDropDownItem),
            new FrameworkPropertyMetadata(typeof(ToolbarDropDownItem)));
    }

    #region 依赖属性

    /// <summary>
    /// 下拉菜单
    /// </summary>
    public static readonly DependencyProperty DropDownMenuProperty =
        DependencyProperty.Register(
            nameof(DropDownMenu),
            typeof(ContextMenu),
            typeof(ToolbarDropDownItem),
            new PropertyMetadata(null));

    public ContextMenu DropDownMenu
    {
        get => (ContextMenu)GetValue(DropDownMenuProperty);
        set => SetValue(DropDownMenuProperty, value);
    }

    /// <summary>
    /// 是否显示下拉菜单
    /// </summary>
    public static readonly DependencyProperty IsDropDownOpenProperty =
        DependencyProperty.Register(
            nameof(IsDropDownOpen),
            typeof(bool),
            typeof(ToolbarDropDownItem),
            new PropertyMetadata(false, OnIsDropDownOpenChanged));

    public bool IsDropDownOpen
    {
        get => (bool)GetValue(IsDropDownOpenProperty);
        set => SetValue(IsDropDownOpenProperty, value);
    }

    #endregion

    #region 事件

    /// <summary>
    /// 下拉菜单打开事件
    /// </summary>
    public static readonly RoutedEvent DropDownOpenedEvent =
        EventManager.RegisterRoutedEvent(
            nameof(DropDownOpened),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(ToolbarDropDownItem));

    public event RoutedEventHandler DropDownOpened
    {
        add => AddHandler(DropDownOpenedEvent, value);
        remove => RemoveHandler(DropDownOpenedEvent, value);
    }

    /// <summary>
    /// 下拉菜单关闭事件
    /// </summary>
    public static readonly RoutedEvent DropDownClosedEvent =
        EventManager.RegisterRoutedEvent(
            nameof(DropDownClosed),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(ToolbarDropDownItem));

    public event RoutedEventHandler DropDownClosed
    {
        add => AddHandler(DropDownClosedEvent, value);
        remove => RemoveHandler(DropDownClosedEvent, value);
    }

    #endregion

    private static void OnIsDropDownOpenChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ToolbarDropDownItem item)
        {
            if ((bool)e.NewValue)
            {
                item.OpenDropDown();
            }
            else
            {
                item.CloseDropDown();
            }
        }
    }

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        base.OnMouseLeftButtonUp(e);
        
        if (IsMouseOver)
        {
            ToggleDropDown();
        }
    }

    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.Enter || e.Key == Key.Space || e.Key == Key.Down)
        {
            ToggleDropDown();
            e.Handled = true;
        }
        else
        {
            base.OnKeyDown(e);
        }
    }

    private void ToggleDropDown()
    {
        IsDropDownOpen = !IsDropDownOpen;
    }

    private void OpenDropDown()
    {
        if (DropDownMenu != null)
        {
            DropDownMenu.PlacementTarget = this;
            DropDownMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Bottom;
            DropDownMenu.IsOpen = true;
            
            DropDownMenu.Closed += OnDropDownMenuClosed;
            RaiseEvent(new RoutedEventArgs(DropDownOpenedEvent, this));
        }
    }

    private void CloseDropDown()
    {
        if (DropDownMenu != null)
        {
            DropDownMenu.IsOpen = false;
            RaiseEvent(new RoutedEventArgs(DropDownClosedEvent, this));
        }
    }

    private void OnDropDownMenuClosed(object sender, RoutedEventArgs e)
    {
        if (sender is ContextMenu menu)
        {
            menu.Closed -= OnDropDownMenuClosed;
        }
        
        IsDropDownOpen = false;
    }

    protected override AutomationPeer OnCreateAutomationPeer()
    {
        return new ToolbarDropDownItemAutomationPeer(this);
    }
}

#region 自动化对等类

public class ToolbarButtonItemAutomationPeer : FrameworkElementAutomationPeer
{
    public ToolbarButtonItemAutomationPeer(ToolbarButtonItem owner) : base(owner)
    {
    }

    protected override string GetClassNameCore() => "ToolbarButtonItem";
    protected override AutomationControlType GetAutomationControlTypeCore() => AutomationControlType.Button;
    protected override string GetLocalizedControlTypeCore() => "工具栏按钮";
}

public class ToolbarDropDownItemAutomationPeer : FrameworkElementAutomationPeer
{
    public ToolbarDropDownItemAutomationPeer(ToolbarDropDownItem owner) : base(owner)
    {
    }

    protected override string GetClassNameCore() => "ToolbarDropDownItem";
    protected override AutomationControlType GetAutomationControlTypeCore() => AutomationControlType.SplitButton;
    protected override string GetLocalizedControlTypeCore() => "工具栏下拉按钮";
}

#endregion
