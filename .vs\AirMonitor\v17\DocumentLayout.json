{"Version": 1, "WorkspaceRootPath": "D:\\项目\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\controls\\toolbaritems.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\toolbaritems.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\converters\\toolbarconverters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\converters\\toolbarconverters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\controls\\themetogglebutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\themetogglebutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ToolbarItems.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ToolbarItems.cs", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ToolbarItems.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ToolbarItems.cs", "RelativeToolTip": "AirMonitor\\Controls\\ToolbarItems.cs", "ViewState": "AgIAAAwAAAAAAAAAAIAywKQAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T14:23:06.403Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ToolbarConverters.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Converters\\ToolbarConverters.cs", "RelativeDocumentMoniker": "AirMonitor\\Converters\\ToolbarConverters.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Converters\\ToolbarConverters.cs", "RelativeToolTip": "AirMonitor\\Converters\\ToolbarConverters.cs", "ViewState": "AgIAADEAAAAAAAAAAADgvz4AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T14:22:54.883Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ThemeToggleButton.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ThemeToggleButton.xaml", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ThemeToggleButton.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ThemeToggleButton.xaml", "RelativeToolTip": "AirMonitor\\Controls\\ThemeToggleButton.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T14:11:44.697Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T14:08:52.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T14:07:37.03Z", "EditorCaption": ""}]}]}]}