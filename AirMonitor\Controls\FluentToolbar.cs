using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace AirMonitor.Controls;

/// <summary>
/// 符合Windows 11 Fluent Design规范的工具栏控件
/// 支持键盘导航、无障碍访问和主题切换
/// </summary>
public class FluentToolbar : ItemsControl
{
    static FluentToolbar()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(FluentToolbar), 
            new FrameworkPropertyMetadata(typeof(FluentToolbar)));
    }

    public FluentToolbar()
    {
        // 启用键盘导航
        KeyboardNavigation.SetTabNavigation(this, KeyboardNavigationMode.Continue);
        KeyboardNavigation.SetDirectionalNavigation(this, KeyboardNavigationMode.Continue);
        
        // 设置默认焦点策略
        Focusable = true;
        IsTabStop = true;
    }

    #region 依赖属性

    /// <summary>
    /// 圆角半径
    /// </summary>
    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(
            nameof(CornerRadius),
            typeof(CornerRadius),
            typeof(FluentToolbar),
            new PropertyMetadata(new CornerRadius(4)));

    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    /// <summary>
    /// 是否显示边框
    /// </summary>
    public static readonly DependencyProperty ShowBorderProperty =
        DependencyProperty.Register(
            nameof(ShowBorder),
            typeof(bool),
            typeof(FluentToolbar),
            new PropertyMetadata(true));

    public bool ShowBorder
    {
        get => (bool)GetValue(ShowBorderProperty);
        set => SetValue(ShowBorderProperty, value);
    }

    /// <summary>
    /// 工具栏方向
    /// </summary>
    public static readonly DependencyProperty OrientationProperty =
        DependencyProperty.Register(
            nameof(Orientation),
            typeof(Orientation),
            typeof(FluentToolbar),
            new PropertyMetadata(Orientation.Horizontal));

    public Orientation Orientation
    {
        get => (Orientation)GetValue(OrientationProperty);
        set => SetValue(OrientationProperty, value);
    }

    #endregion

    #region 键盘导航支持

    protected override void OnKeyDown(KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Left:
                if (Orientation == Orientation.Horizontal)
                {
                    MoveFocusToPreviousItem();
                    e.Handled = true;
                }
                break;

            case Key.Right:
                if (Orientation == Orientation.Horizontal)
                {
                    MoveFocusToNextItem();
                    e.Handled = true;
                }
                break;

            case Key.Up:
                if (Orientation == Orientation.Vertical)
                {
                    MoveFocusToPreviousItem();
                    e.Handled = true;
                }
                break;

            case Key.Down:
                if (Orientation == Orientation.Vertical)
                {
                    MoveFocusToNextItem();
                    e.Handled = true;
                }
                break;

            case Key.Home:
                MoveFocusToFirstItem();
                e.Handled = true;
                break;

            case Key.End:
                MoveFocusToLastItem();
                e.Handled = true;
                break;

            case Key.Enter:
            case Key.Space:
                if (GetFocusedItem() is ICommandSource commandSource && commandSource.Command?.CanExecute(commandSource.CommandParameter) == true)
                {
                    commandSource.Command.Execute(commandSource.CommandParameter);
                    e.Handled = true;
                }
                break;
        }

        base.OnKeyDown(e);
    }

    private void MoveFocusToPreviousItem()
    {
        var focusableItems = GetFocusableItems();
        var currentIndex = GetCurrentFocusedIndex(focusableItems);
        
        if (currentIndex > 0)
        {
            focusableItems[currentIndex - 1].Focus();
        }
        else if (focusableItems.Count > 0)
        {
            focusableItems[^1].Focus(); // 循环到最后一个
        }
    }

    private void MoveFocusToNextItem()
    {
        var focusableItems = GetFocusableItems();
        var currentIndex = GetCurrentFocusedIndex(focusableItems);
        
        if (currentIndex < focusableItems.Count - 1)
        {
            focusableItems[currentIndex + 1].Focus();
        }
        else if (focusableItems.Count > 0)
        {
            focusableItems[0].Focus(); // 循环到第一个
        }
    }

    private void MoveFocusToFirstItem()
    {
        var focusableItems = GetFocusableItems();
        if (focusableItems.Count > 0)
        {
            focusableItems[0].Focus();
        }
    }

    private void MoveFocusToLastItem()
    {
        var focusableItems = GetFocusableItems();
        if (focusableItems.Count > 0)
        {
            focusableItems[^1].Focus();
        }
    }

    private List<FrameworkElement> GetFocusableItems()
    {
        var focusableItems = new List<FrameworkElement>();
        
        for (int i = 0; i < Items.Count; i++)
        {
            var container = ItemContainerGenerator.ContainerFromIndex(i) as FrameworkElement;
            if (container != null && container.IsEnabled && container.Focusable)
            {
                focusableItems.Add(container);
            }
        }
        
        return focusableItems;
    }

    private int GetCurrentFocusedIndex(List<FrameworkElement> focusableItems)
    {
        var focusedElement = Keyboard.FocusedElement as FrameworkElement;
        return focusableItems.IndexOf(focusedElement);
    }

    private FrameworkElement? GetFocusedItem()
    {
        return Keyboard.FocusedElement as FrameworkElement;
    }

    #endregion

    #region 无障碍访问支持

    protected override void OnGotFocus(RoutedEventArgs e)
    {
        base.OnGotFocus(e);
        
        // 如果工具栏获得焦点但没有子项有焦点，则将焦点移到第一个可聚焦的项
        if (Keyboard.FocusedElement == this)
        {
            MoveFocusToFirstItem();
        }
    }

    protected override AutomationPeer OnCreateAutomationPeer()
    {
        return new FluentToolbarAutomationPeer(this);
    }

    #endregion
}

/// <summary>
/// FluentToolbar的自动化对等类，用于无障碍访问支持
/// </summary>
public class FluentToolbarAutomationPeer : ItemsControlAutomationPeer
{
    public FluentToolbarAutomationPeer(FluentToolbar owner) : base(owner)
    {
    }

    protected override string GetClassNameCore()
    {
        return "FluentToolbar";
    }

    protected override AutomationControlType GetAutomationControlTypeCore()
    {
        return AutomationControlType.ToolBar;
    }

    protected override string GetLocalizedControlTypeCore()
    {
        return "工具栏";
    }
}
