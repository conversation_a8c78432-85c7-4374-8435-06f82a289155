<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Dark Theme - 暗色主题
         符合Windows 11 Fluent Design暗色主题规范
         ======================================== -->

    <!-- 合并基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="../Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 暗色主题专用色彩定义 -->
    <!-- 背景色系 (暗色) -->
    <Color x:Key="SystemBaseColorHighDark">#1C1C1C</Color>
    <Color x:Key="SystemBaseColorMediumHighDark">#2C2C2C</Color>
    <Color x:Key="SystemBaseColorMediumDark">#3C3C3C</Color>
    <Color x:Key="SystemBaseColorMediumLowDark">#4C4C4C</Color>
    <Color x:Key="SystemBaseColorLowDark">#5C5C5C</Color>
    
    <!-- 文本色系 (暗色) -->
    <Color x:Key="SystemBaseForegroundColorHighDark">#FFFFFF</Color>
    <Color x:Key="SystemBaseForegroundColorMediumHighDark">#E0E0E0</Color>
    <Color x:Key="SystemBaseForegroundColorMediumDark">#CCCCCC</Color>
    <Color x:Key="SystemBaseForegroundColorMediumLowDark">#999999</Color>
    <Color x:Key="SystemBaseForegroundColorLowDark">#666666</Color>
    
    <!-- 边框色系 (暗色) -->
    <Color x:Key="SystemBorderColorHighDark">#666666</Color>
    <Color x:Key="SystemBorderColorMediumDark">#4C4C4C</Color>
    <Color x:Key="SystemBorderColorLowDark">#3C3C3C</Color>

    <!-- 主色调画刷 (Primary Brushes) -->
    <SolidColorBrush x:Key="SystemAccentBrush" Color="{StaticResource SystemAccentColorPrimary}"/>
    <SolidColorBrush x:Key="SystemAccentBrushSecondary" Color="{StaticResource SystemAccentColorSecondary}"/>
    <SolidColorBrush x:Key="SystemAccentBrushTertiary" Color="{StaticResource SystemAccentColorTertiary}"/>
    
    <!-- 主色调变体画刷 -->
    <SolidColorBrush x:Key="SystemAccentBrushLight1" Color="{StaticResource SystemAccentColorLight1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight2" Color="{StaticResource SystemAccentColorLight2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight3" Color="{StaticResource SystemAccentColorLight3}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark1" Color="{StaticResource SystemAccentColorDark1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark2" Color="{StaticResource SystemAccentColorDark2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark3" Color="{StaticResource SystemAccentColorDark3}"/>

    <!-- 背景画刷 (Background Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="ApplicationBackgroundBrush" Color="{StaticResource SystemBaseColorHighDark}"/>
    <SolidColorBrush x:Key="LayerBackgroundBrush" Color="{StaticResource SystemBaseColorMediumHighDark}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource SystemBaseColorMediumHighDark}"/>
    <SolidColorBrush x:Key="SurfaceBackgroundBrush" Color="{StaticResource SystemBaseColorMediumDark}"/>
    <SolidColorBrush x:Key="SubtleBackgroundBrush" Color="{StaticResource SystemBaseColorMediumLowDark}"/>
    
    <!-- 文本画刷 (Text Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource SystemBaseForegroundColorMediumHighDark}"/>
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource SystemBaseForegroundColorMediumDark}"/>
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource SystemBaseForegroundColorMediumLowDark}"/>
    <SolidColorBrush x:Key="TextFillColorInverseBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    
    <!-- 边框画刷 (Border Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="ControlStrokeColorDefaultBrush" Color="{StaticResource SystemBorderColorHighDark}"/>
    <SolidColorBrush x:Key="ControlStrokeColorSecondaryBrush" Color="{StaticResource SystemBorderColorMediumDark}"/>
    <SolidColorBrush x:Key="ControlStrokeColorTertiaryBrush" Color="{StaticResource SystemBorderColorLowDark}"/>
    <SolidColorBrush x:Key="DividerStrokeColorDefaultBrush" Color="{StaticResource SystemBorderColorMediumDark}"/>
    
    <!-- 控件填充画刷 (Control Fill Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="ControlFillColorDefaultBrush" Color="{StaticResource SystemBaseColorMediumDark}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryBrush" Color="{StaticResource SystemBaseColorMediumLowDark}"/>
    <SolidColorBrush x:Key="ControlFillColorTertiaryBrush" Color="{StaticResource SystemBaseColorLowDark}"/>
    <SolidColorBrush x:Key="ControlFillColorDisabledBrush" Color="{StaticResource SystemBaseColorMediumHighDark}"/>
    <SolidColorBrush x:Key="ControlFillColorInputActiveBrush" Color="{StaticResource SystemBaseColorMediumHighDark}"/>
    
    <!-- 语义色画刷 (Semantic Brushes) -->
    <SolidColorBrush x:Key="SystemFillColorSuccessBrush" Color="{StaticResource SystemSuccessColor}"/>
    <SolidColorBrush x:Key="SystemFillColorSuccessBackgroundBrush" Color="#1A107C10"/>
    <SolidColorBrush x:Key="SystemFillColorWarningBrush" Color="{StaticResource SystemWarningColor}"/>
    <SolidColorBrush x:Key="SystemFillColorWarningBackgroundBrush" Color="#1AFF8C00"/>
    <SolidColorBrush x:Key="SystemFillColorCriticalBrush" Color="{StaticResource SystemErrorColor}"/>
    <SolidColorBrush x:Key="SystemFillColorCriticalBackgroundBrush" Color="#1AD13438"/>
    <SolidColorBrush x:Key="SystemFillColorNeutralBrush" Color="{StaticResource SystemInfoColor}"/>
    <SolidColorBrush x:Key="SystemFillColorNeutralBackgroundBrush" Color="#1A0078D4"/>
    
    <!-- 透明度画刷 (Opacity Brushes) -->
    <SolidColorBrush x:Key="SystemAccentBrush10" Color="{StaticResource SystemAccentColor10}"/>
    <SolidColorBrush x:Key="SystemAccentBrush20" Color="{StaticResource SystemAccentColor20}"/>
    <SolidColorBrush x:Key="SystemAccentBrush30" Color="{StaticResource SystemAccentColor30}"/>
    <SolidColorBrush x:Key="SystemAccentBrush40" Color="{StaticResource SystemAccentColor40}"/>
    <SolidColorBrush x:Key="SystemAccentBrush50" Color="{StaticResource SystemAccentColor50}"/>
    <SolidColorBrush x:Key="SystemAccentBrush60" Color="{StaticResource SystemAccentColor60}"/>
    <SolidColorBrush x:Key="SystemAccentBrush70" Color="{StaticResource SystemAccentColor70}"/>
    <SolidColorBrush x:Key="SystemAccentBrush80" Color="{StaticResource SystemAccentColor80}"/>
    <SolidColorBrush x:Key="SystemAccentBrush90" Color="{StaticResource SystemAccentColor90}"/>
    
    <!-- 暗色主题专用透明度画刷 -->
    <SolidColorBrush x:Key="SystemBaseBrush10" Color="#1AFFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush20" Color="#33FFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush30" Color="#4DFFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush40" Color="#66FFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush50" Color="#80FFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush60" Color="#99FFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush70" Color="#B3FFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush80" Color="#CCFFFFFF"/>
    <SolidColorBrush x:Key="SystemBaseBrush90" Color="#E6FFFFFF"/>

    <!-- 悬停状态画刷 (Hover State Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="ControlFillColorDefaultHoverBrush" Color="#4DFFFFFF"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryHoverBrush" Color="#66FFFFFF"/>
    <SolidColorBrush x:Key="SystemAccentBrushHover" Color="{StaticResource SystemAccentColorSecondary}"/>
    
    <!-- 按下状态画刷 (Pressed State Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="ControlFillColorDefaultPressedBrush" Color="#80FFFFFF"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryPressedBrush" Color="#99FFFFFF"/>
    <SolidColorBrush x:Key="SystemAccentBrushPressed" Color="{StaticResource SystemAccentColorTertiary}"/>
    
    <!-- 焦点状态画刷 (Focus State Brushes) - 暗色主题 -->
    <SolidColorBrush x:Key="FocusStrokeColorOuterBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    <SolidColorBrush x:Key="FocusStrokeColorInnerBrush" Color="{StaticResource SystemBaseColorHighDark}"/>

    <!-- ========================================
         Toolbar Specific Color Brushes - Dark Theme
         工具栏专用颜色画刷 - 暗色主题
         ======================================== -->

    <!-- 工具栏背景颜色 -->
    <SolidColorBrush x:Key="ToolbarBackgroundBrush" Color="{StaticResource SystemBaseColorMediumHighDark}"/>
    <SolidColorBrush x:Key="ToolbarBorderBrush" Color="{StaticResource SystemBorderColorLowDark}"/>

    <!-- 工具栏按钮颜色 -->
    <SolidColorBrush x:Key="ToolbarButtonBackgroundBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="ToolbarButtonBackgroundPointerOverBrush" Color="{StaticResource SystemBaseColorMediumDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonBackgroundPressedBrush" Color="{StaticResource SystemBaseColorMediumLowDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonBackgroundDisabledBrush" Color="Transparent"/>

    <SolidColorBrush x:Key="ToolbarButtonForegroundBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonForegroundPointerOverBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonForegroundPressedBrush" Color="{StaticResource SystemBaseForegroundColorHighDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonForegroundDisabledBrush" Color="{StaticResource SystemBaseForegroundColorMediumLowDark}"/>

    <SolidColorBrush x:Key="ToolbarButtonBorderBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="ToolbarButtonBorderPointerOverBrush" Color="{StaticResource SystemBorderColorMediumDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonBorderPressedBrush" Color="{StaticResource SystemBorderColorHighDark}"/>
    <SolidColorBrush x:Key="ToolbarButtonBorderDisabledBrush" Color="Transparent"/>

    <!-- 工具栏分隔符颜色 -->
    <SolidColorBrush x:Key="ToolbarSeparatorBrush" Color="{StaticResource SystemBorderColorMediumDark}"/>

    <!-- 工具栏下拉按钮颜色 -->
    <SolidColorBrush x:Key="ToolbarDropDownButtonBackgroundBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="ToolbarDropDownButtonBackgroundPointerOverBrush" Color="{StaticResource SystemBaseColorMediumDark}"/>
    <SolidColorBrush x:Key="ToolbarDropDownButtonBackgroundPressedBrush" Color="{StaticResource SystemBaseColorMediumLowDark}"/>
    <SolidColorBrush x:Key="ToolbarDropDownButtonBackgroundDisabledBrush" Color="Transparent"/>

</ResourceDictionary>
